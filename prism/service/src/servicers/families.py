import logging
import yaml
from datetime import timedelta

from gametime_protos.mlp.prism.v1.service_pb2 import (
    FamiliesServiceCreateResponse,
    FamiliesServiceDeleteResponse,
    FamiliesServiceDescribeResponse,
    FamiliesServiceListResponse,
    FamiliesServiceUpsertFromYAMLRequest,
    FamiliesServiceUpsertFromYAMLResponse,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import (
    FamiliesServiceServicer,
    add_FamiliesServiceServicer_to_server,
)
from grpc import StatusCode

from src.families import (
    Family,
    FamilyInvalidError,
    FamilyAlreadyExistsError,
    FamilyNotFoundError,
    _parse_aggregation_function_string,
)
from src.shared import FamilyDetails, TASK_QUEUE_NAME
from src.workflows import FamilyPipeline, DEFAULT_RETRY_POLICY


register_servicer = add_FamiliesServiceServicer_to_server


class Servicer(FamiliesServiceServicer):
    def __init__(self, registry, temporal_wrapper):
        self.registry = registry
        self.temporal_wrapper = temporal_wrapper

    async def Create(self, request, context):
        try:
            family = Family.from_proto(request)
            await self.registry.add(family, allow_update=False)
            family_details = FamilyDetails(name=family.name)
            workflow_id = FamilyPipeline.id_for(family_details)

            try:
                existing_handle = self.temporal_wrapper.client.get_workflow_handle(
                    workflow_id
                )
                await existing_handle.terminate(
                    reason="New family creation request, replacing existing workflow."
                )
                logging.info(
                    f"Terminated existing workflow {workflow_id} for family {family.name} due to new Create request."
                )
            except Exception:
                logging.debug(
                    f"No existing workflow {workflow_id} found to terminate for family {family.name}, or already completed."
                )
                pass

            await self.temporal_wrapper.client.start_workflow(
                FamilyPipeline.run,
                family_details,
                id=workflow_id,
                task_queue=TASK_QUEUE_NAME,
            )
        except FamilyInvalidError as e:
            await context.abort(StatusCode.INVALID_ARGUMENT, str(e))
        except FamilyAlreadyExistsError as e:
            await context.abort(StatusCode.ALREADY_EXISTS, str(e))
        return FamiliesServiceCreateResponse()

    async def UpsertFromYAML(
        self, request: FamiliesServiceUpsertFromYAMLRequest, context
    ):
        try:
            yaml_content_str = request.yaml_content
            if not yaml_content_str:
                await context.abort(
                    StatusCode.INVALID_ARGUMENT, "YAML content cannot be empty."
                )
                return FamiliesServiceUpsertFromYAMLResponse()

            try:
                data_dict = yaml.safe_load(yaml_content_str)
            except yaml.YAMLError as e:
                await context.abort(StatusCode.INVALID_ARGUMENT, f"Invalid YAML: {e}")
                return FamiliesServiceUpsertFromYAMLResponse()

            if not isinstance(data_dict, dict):
                await context.abort(
                    StatusCode.INVALID_ARGUMENT, "YAML root must be a dictionary."
                )
                return FamiliesServiceUpsertFromYAMLResponse()

            if "config" in data_dict and "features" in data_dict["config"]:
                for feature_config_dict in data_dict["config"]["features"]:
                    if "aggregations" in feature_config_dict and isinstance(
                        feature_config_dict["aggregations"], list
                    ):
                        parsed_aggs = []
                        for agg_str in feature_config_dict["aggregations"]:
                            enum_val = _parse_aggregation_function_string(agg_str)
                            if enum_val:
                                parsed_aggs.append(enum_val.value)
                            else:
                                await context.abort(
                                    StatusCode.INVALID_ARGUMENT,
                                    f"Invalid aggregation function string: {agg_str}",
                                )
                                return FamiliesServiceUpsertFromYAMLResponse()
                        feature_config_dict["aggregations"] = parsed_aggs

            if "name" not in data_dict:
                await context.abort(
                    StatusCode.INVALID_ARGUMENT,
                    "Family 'name' missing in YAML content.",
                )
                return FamiliesServiceUpsertFromYAMLResponse()

            try:
                for field_to_remove in [
                    "id",
                    "inserted_at",
                    "status",
                    "status_detail",
                    "last_fetched_at",
                    "frontier",
                    "pipeline_settings",
                    "draft",
                ]:
                    data_dict.pop(field_to_remove, None)
                family_from_yaml = Family.from_dict(data_dict)
            except Exception as e:
                await context.abort(
                    StatusCode.INVALID_ARGUMENT,
                    f"Error converting YAML to Family object: {e}",
                )
                return FamiliesServiceUpsertFromYAMLResponse()

            family = await self.registry.add_or_update_production_family(
                family_from_yaml
            )

            family_details = FamilyDetails(name=family.name)
            workflow_id = FamilyPipeline.id_for(family_details)

            try:
                existing_handle = self.temporal_wrapper.client.get_workflow_handle(
                    workflow_id
                )
                await existing_handle.terminate(
                    reason="Family configuration updated via YAML, restarting pipeline."
                )
                logging.info(
                    f"Terminated existing workflow {workflow_id} for family {family.name} due to YAML upsert."
                )
            except Exception:
                logging.debug(
                    f"No existing workflow {workflow_id} found to terminate for family {family.name}, or already completed."
                )
                pass

            try:
                await self.temporal_wrapper.client.execute_activity(
                    "delete_schedule",
                    args=[family_details],
                    start_to_close_timeout=timedelta(seconds=30),
                    retry_policy=DEFAULT_RETRY_POLICY,
                    task_queue=TASK_QUEUE_NAME,
                )
                logging.info(
                    f"Deleted existing schedule for family {family.name} due to YAML upsert."
                )
            except Exception as e:
                logging.info(
                    f"Could not delete schedule for family {family.name} (may not exist): {e}"
                )
                pass

            await self.temporal_wrapper.client.start_workflow(
                FamilyPipeline.run,
                family_details,
                id=workflow_id,
                task_queue=TASK_QUEUE_NAME,
            )
            return FamiliesServiceUpsertFromYAMLResponse(family=family.to_proto())

        except FamilyInvalidError as e:
            await context.abort(StatusCode.INVALID_ARGUMENT, str(e))
        except FamilyAlreadyExistsError as e:
            await context.abort(
                StatusCode.INTERNAL, f"Unexpected FamilyAlreadyExistsError: {e}"
            )
        except FamilyNotFoundError as e:
            await context.abort(
                StatusCode.INTERNAL, f"Unexpected FamilyNotFoundError: {e}"
            )
        except Exception as e:
            logging.error(f"Unexpected error in UpsertFromYAML: {e}", exc_info=True)
            await context.abort(
                StatusCode.INTERNAL, f"An unexpected error occurred: {e}"
            )
            return FamiliesServiceUpsertFromYAMLResponse()

    async def Delete(self, request, context):
        family_details = FamilyDetails(name=request.name)
        try:
            try:
                await self.temporal_wrapper.client.execute_activity(
                    "delete_schedule",
                    args=[family_details],
                    start_to_close_timeout=timedelta(seconds=30),
                    retry_policy=DEFAULT_RETRY_POLICY,
                    task_queue=TASK_QUEUE_NAME,
                )
                logging.info(
                    f"Successfully deleted schedule for family {request.name} during delete operation."
                )
            except Exception as schedule_delete_err:
                logging.warning(
                    f"Failed to delete schedule for family {request.name} during delete operation (may not exist): {schedule_delete_err}",
                )

            pipeline_workflow_id = FamilyPipeline.id_for(family_details)
            try:
                await self.temporal_wrapper.client.get_workflow_handle(
                    pipeline_workflow_id
                ).terminate("Family deleted by user request")
                logging.info(
                    f"Terminated workflow {pipeline_workflow_id} for family {request.name}"
                )
            except Exception:
                logging.info(
                    f"Workflow {pipeline_workflow_id} for family {request.name} not found or already completed during delete."
                )
                pass

            await self.registry.remove(request.name)
        except FamilyNotFoundError as e:
            await context.abort(StatusCode.NOT_FOUND, str(e))
        except Exception as e:
            logging.error(
                f"Unexpected error in Delete family {request.name}: {e}", exc_info=True
            )
            await context.abort(
                StatusCode.INTERNAL,
                f"An unexpected error occurred during deletion: {e}",
            )
        return FamiliesServiceDeleteResponse()

    async def Describe(self, request, context):
        try:
            family = await self.registry.fetch_one(request.name)
        except FamilyNotFoundError as e:
            await context.abort(StatusCode.NOT_FOUND, str(e))
        return FamiliesServiceDescribeResponse(family=family.to_proto())

    async def List(self, request, context):
        families = await self.registry.fetch_all()
        return FamiliesServiceListResponse(families=[f.to_proto() for f in families])
